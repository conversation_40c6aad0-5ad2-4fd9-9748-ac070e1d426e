import { Injectable } from '@nestjs/common';

import { ExtendedCacheService, ItemCacheKeyConfig, ItemsCacheKeyConfig } from '@/infra/cache/extended-cache.service';
import { CacheScopes, CacheCategories, CacheCommonKeys } from '@/shared/constants/cache';
import { groupBy } from '@/shared/utils/collection';
import { completeObject } from '@/shared/utils/object';

import { PoolOrBracket, PoolOrBracketShortInfo } from './entities';
import {
	FetchRoundsPoolBracketsParams,
	FetchDivisionPoolBracketsParams,
	FetchMatchesPoolBracketsShortInfoParams,
	PoolBracketRepository,
} from './pool-bracket.repository';

@Injectable()
export class PoolBracketService {
	constructor(private poolBracketRepository: PoolBracketRepository, private cacheService: ExtendedCacheService) {}

	fetchRoundsPoolBracketsMap(params: FetchRoundsPoolBracketsParams): Promise<Record<string, (typeof PoolOrBracket)[]>> {
		return this.cacheService.getOrFetchMany<(typeof PoolOrBracket)[]>(
			this._getRoundsPoolBracketsCacheKeyConfig(params),
			params.roundsIds,
			(missingIds) => this._getRoundsPoolBracketsData({ ...params, roundsIds: missingIds }),
		);
	}

	fetchDivisionPoolBrackets(params: FetchDivisionPoolBracketsParams): Promise<(typeof PoolOrBracket)[]> {
		return this.cacheService.getOrFetch<(typeof PoolOrBracket)[]>(this._getDivisionPoolBracketsCacheKeyConfig(params), () =>
			this.poolBracketRepository.fetchDivisionPoolBrackets(params),
		);
	}

	fetchMatchesPoolBracketsShortInfoMap(
		params: FetchMatchesPoolBracketsShortInfoParams,
	): Promise<Record<string, PoolOrBracketShortInfo | null>> {
		return this.cacheService.getOrFetchMany<PoolOrBracketShortInfo | null>(
			this._getMatchesPoolBracketsShortInfoCacheKeyConfig(),
			params.matchesIds,
			(missingIds) => this._getMatchesPoolBracketsShortInfoData({ ...params, matchesIds: missingIds }),
		);
	}

	private async _getRoundsPoolBracketsData(params: FetchRoundsPoolBracketsParams): Promise<Record<string, (typeof PoolOrBracket)[]>> {
		const roundsPoolBrackets = await this.poolBracketRepository.fetchRoundsPoolBrackets(params);
		const roundsPoolBracketsMap = groupBy(roundsPoolBrackets, 'round_id');
		return completeObject(params.roundsIds, roundsPoolBracketsMap, []);
	}

	private async _getMatchesPoolBracketsShortInfoData(
		params: FetchMatchesPoolBracketsShortInfoParams,
	): Promise<Record<string, PoolOrBracketShortInfo | null>> {
		const poolBracketsShortInfoMap = await this.poolBracketRepository.fetchMatchesPoolBracketsShortInfoMap(params);
		return completeObject(params.matchesIds, poolBracketsShortInfoMap, null);
	}

	private _getRoundsPoolBracketsCacheKeyConfig(params: FetchRoundsPoolBracketsParams): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventId,
			category: CacheCategories.RoundPoolBrackets,
		};
	}

	private _getDivisionPoolBracketsCacheKeyConfig(params: FetchDivisionPoolBracketsParams): ItemCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventKey.value,
			category: CacheCategories.DivisionPoolBrackets,
			categoryKey: params.divisionId,
		};
	}

	private _getMatchesPoolBracketsShortInfoCacheKeyConfig(): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: CacheCommonKeys.All,
			category: CacheCategories.MatchesPoolBracketsShortInfo,
		};
	}
}
