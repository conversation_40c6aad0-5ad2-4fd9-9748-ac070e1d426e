import { Module } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { EventHelperModule } from '@/infra/eventHelper/eventHelper.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { TEAMS_INDEX_SCHEMA } from './constants';
import {
	TeamsDivisionStandingLoader,
	TeamsMatchesLoader,
	TeamsNextMatchLoader,
	TeamsFinishedMatchesLoader,
	TeamsUpcomingMatchesLoader,
} from './loaders';
import { TeamSearchRepository } from './team-search.repository';
import { TeamSearchService } from './team-search.service';
import { TeamRepository } from './team.repository';
import { TeamResolver } from './team.resolver';
import { TeamService } from './team.service';
import { DivisionStandingModule } from '../divisionStanding/division-standing.module';
import { MatchModule } from '../match/match.module';
import { SearchModule } from '../search/search.module';
import { SearchEntityName } from '../search/types';

@Module({
	imports: [
		PrismaModule,
		CacheModule,
		MatchModule,
		DivisionStandingModule,
		EventHelperModule,
		SearchModule.forFeature(SearchEntityName.Teams, TEAMS_INDEX_SCHEMA),
	],
	providers: [
		TeamResolver,
		TeamService,
		TeamRepository,
		TeamSearchRepository,
		TeamSearchService,
		TeamsMatchesLoader,
		TeamsNextMatchLoader,
		TeamsDivisionStandingLoader,
		TeamsFinishedMatchesLoader,
		TeamsUpcomingMatchesLoader,
	],
	exports: [TeamService],
})
export class TeamModule {}
